#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import requests
import zipfile
import os
import json
from pathlib import Path
from urllib.parse import urlparse
import time

def read_admin_codes(csv_file):
    """
    从CSV文件中读取行政区划代码，仅返回末尾为00的代码
    """
    admin_codes = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)  # 跳过标题行
            
            for row in reader:
                if len(row) >= 2:
                    code = row[0].strip()
                    name = row[1].strip()
                    
                    # 仅处理末尾为00的行政区划代码
                    if code.endswith('00'):
                        admin_codes.append((code, name))
                        
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        
    return admin_codes

def load_progress(progress_file):
    """
    加载处理进度
    """
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    return {"processed": [], "failed": []}

def save_progress(progress_file, progress):
    """
    保存处理进度
    """
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存进度时出错: {e}")

def download_map_file(adcode, api_key, download_dir):
    """
    从高德地图API下载地图文件
    """
    api_url = f"https://restapi.amap.com/v5/rtos/raster/privince_pack?thin=0&adcode={adcode}&zip=0&key={api_key}"
    
    try:
        print(f"正在获取 {adcode} 的下载地址...")
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('infocode') != '10000':
            print(f"API返回错误: {data.get('info', '未知错误')}")
            return None
            
        download_url = data.get('url')
        if not download_url:
            print(f"未获取到 {adcode} 的下载地址")
            return None
            
        # 从URL中提取文件名
        parsed_url = urlparse(download_url)
        filename = os.path.basename(parsed_url.path)
        if not filename.endswith('.zip'):
            filename = f"{adcode}.zip"
            
        file_path = download_dir / filename
        
        # 检查文件是否已存在
        if file_path.exists():
            print(f"文件已存在，跳过下载: {filename}")
            return file_path
            
        print(f"正在下载: {filename}")
        
        # 下载文件
        download_response = requests.get(download_url, timeout=300, stream=True)
        download_response.raise_for_status()
        
        with open(file_path, 'wb') as f:
            for chunk in download_response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    
        print(f"下载完成: {filename}")
        return file_path
        
    except requests.exceptions.RequestException as e:
        print(f"下载 {adcode} 时网络错误: {e}")
        return None
    except Exception as e:
        print(f"下载 {adcode} 时出错: {e}")
        return None

def extract_folders_from_zip(zip_file, target_dir):
    """
    从zip文件中提取数字命名为6-13的文件夹到目标目录
    合并同名文件夹，跳过同名文件
    """
    extracted_files = 0
    skipped_files = 0
    
    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            # 获取zip文件中的所有文件和文件夹路径
            all_paths = zip_ref.namelist()
            
            # 找到所有数字命名的文件夹（6-13）
            target_folders = {}  # folder_name -> full_path_in_zip
            
            for path in all_paths:
                # 清理路径，移除末尾的斜杠
                clean_path = path.rstrip('/')
                path_parts = clean_path.split('/')
                
                # 检查路径中的每个部分
                for i, part in enumerate(path_parts):
                    if part.isdigit() and 6 <= int(part) <= 13:
                        folder_name = part
                        # 构建到该文件夹的完整路径
                        folder_path = '/'.join(path_parts[:i+1])
                        
                        # 只保留最深层的路径（最里层的文件夹）
                        if folder_name not in target_folders or len(folder_path.split('/')) > len(target_folders[folder_name].split('/')):
                            target_folders[folder_name] = folder_path
            
            # 提取找到的文件夹
            for folder_name, folder_path_in_zip in target_folders.items():
                target_folder = target_dir / folder_name
                
                # 创建目标文件夹（如果不存在）
                target_folder.mkdir(exist_ok=True)
                
                print(f"    处理文件夹: {folder_name} (来源: {folder_path_in_zip})")
                
                # 提取该文件夹下的所有文件
                folder_extracted = 0
                folder_skipped = 0
                
                for file_path in all_paths:
                    # 检查文件是否在目标文件夹内
                    if file_path.startswith(folder_path_in_zip + '/'):
                        # 计算相对于文件夹的路径
                        relative_path = file_path[len(folder_path_in_zip) + 1:]
                        
                        if relative_path:  # 不是空路径
                            target_file = target_folder / relative_path
                            
                            # 如果是文件夹路径（以/结尾），创建目录
                            if file_path.endswith('/'):
                                target_file.mkdir(parents=True, exist_ok=True)
                            else:
                                # 检查文件是否已存在
                                if target_file.exists():
                                    folder_skipped += 1
                                    continue
                                
                                # 确保目标文件的目录存在
                                target_file.parent.mkdir(parents=True, exist_ok=True)
                                
                                # 提取文件
                                with zip_ref.open(file_path) as source_file:
                                    with open(target_file, 'wb') as target_file_obj:
                                        target_file_obj.write(source_file.read())
                                folder_extracted += 1
                
                if folder_extracted > 0 or folder_skipped > 0:
                    print(f"      新提取: {folder_extracted} 个文件, 跳过: {folder_skipped} 个重复文件")
                
                extracted_files += folder_extracted
                skipped_files += folder_skipped
                
    except zipfile.BadZipFile:
        print(f"  错误: {zip_file.name} 不是有效的zip文件")
        return 0, 0
    except Exception as e:
        print(f"处理 {zip_file.name} 时出错: {e}")
        return 0, 0
        
    return extracted_files, skipped_files

def main():
    """
    主函数 - 完整版本
    """
    # 配置参数
    csv_file = "code.csv"
    api_key = "f35f73c2fa74597876d5f54658e4e1ca"
    download_dir = Path(".")  # 当前目录
    target_dir = Path(r"C:\Users\<USER>\Desktop\map1")
    progress_file = "download_progress.json"
    
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    print("=== 开始处理行政区划地图下载和提取 ===")
    
    # 加载进度
    progress = load_progress(progress_file)
    processed_codes = set(progress.get("processed", []))
    failed_codes = set(progress.get("failed", []))
    
    # 1. 读取行政区划代码
    print("\n1. 读取行政区划代码...")
    admin_codes = read_admin_codes(csv_file)
    print(f"找到 {len(admin_codes)} 个需要处理的行政区划代码")
    print(f"已处理: {len(processed_codes)} 个")
    print(f"已失败: {len(failed_codes)} 个")
    
    # 过滤已处理的代码
    remaining_codes = [(code, name) for code, name in admin_codes 
                      if code not in processed_codes and code not in failed_codes]
    print(f"剩余需处理: {len(remaining_codes)} 个")
    
    # 统计信息
    total_downloaded = 0
    total_extracted_files = 0
    total_skipped_files = 0
    
    # 2. 下载和处理每个行政区划
    for i, (adcode, name) in enumerate(remaining_codes, 1):
        print(f"\n2. 处理 {i}/{len(remaining_codes)}: {adcode} - {name}")
        
        try:
            # 下载地图文件
            zip_file = download_map_file(adcode, api_key, download_dir)
            
            if zip_file and zip_file.exists():
                total_downloaded += 1
                
                # 提取文件夹
                print(f"  正在提取文件夹...")
                extracted, skipped = extract_folders_from_zip(zip_file, target_dir)
                total_extracted_files += extracted
                total_skipped_files += skipped
                
                # 标记为已处理
                processed_codes.add(adcode)
            else:
                # 标记为失败
                failed_codes.add(adcode)
                
        except KeyboardInterrupt:
            print(f"\n用户中断，已处理 {i-1}/{len(remaining_codes)} 个行政区划")
            break
        except Exception as e:
            print(f"  处理 {adcode} 时出错: {e}")
            failed_codes.add(adcode)
            
        # 保存进度
        progress["processed"] = list(processed_codes)
        progress["failed"] = list(failed_codes)
        save_progress(progress_file, progress)
            
        # 添加延迟避免API限制
        time.sleep(0.5)
        
        # 每处理10个显示一次进度
        if i % 10 == 0:
            print(f"\n--- 进度报告 ---")
            print(f"已处理: {i}/{len(remaining_codes)} ({i/len(remaining_codes)*100:.1f}%)")
            print(f"成功下载: {total_downloaded}")
            print(f"失败: {len(failed_codes)}")
    
    # 3. 输出统计结果
    print(f"\n=== 处理完成 ===")
    print(f"总行政区划: {len(admin_codes)} 个")
    print(f"成功处理: {len(processed_codes)} 个")
    print(f"失败处理: {len(failed_codes)} 个")
    print(f"本次提取: {total_extracted_files} 个文件")
    print(f"本次跳过: {total_skipped_files} 个重复文件")
    
    # 检查目标目录中的文件夹
    if target_dir.exists():
        folders = [f.name for f in target_dir.iterdir() if f.is_dir() and f.name.isdigit() and 6 <= int(f.name) <= 13]
        folders.sort(key=int)
        print(f"目标目录中的文件夹: {folders}")

if __name__ == "__main__":
    main()
