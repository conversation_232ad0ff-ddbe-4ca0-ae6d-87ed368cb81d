
1. 从`code.csv`中读取行政区划代码，仅处理最后两位数是`00`的行政区划代码，其他的行政区划代码不处理。

2. 从以下接口获取各个需要处理的行政区地图的下载地址，并将地图文件下载到当前路径，如果存在同名的文件，跳过下载。
https://restapi.amap.com/v5/rtos/raster/privince_pack?thin=0&adcode=150900&zip=0&key=f35f73c2fa74597876d5f54658e4e1ca
接口返回示例如下，其中url为下载地址：
```json
{
"infocode": "10000",
"info": "OK",
"version": "20241017",
"adcode": "150900",
"url": "http://open-platform.cn-zhangjiakou.oss.aliyuncs.com/lbs_raster/province_pck/base/20241017/origin/150000_150900.zip?Expires=1754825342&OSSAccessKeyId=rHE3i3H5uIfsClJs&Signature=v8MiIZoMq8PPOh3hT%2Fyn4Jk2jQI%3D"
}
```

3. 下载下来的地图文件中最里层为6-16数字命名的文件夹，将各个压缩文件最里层的6-13数字命名的文件夹复制到`C:\Users\<USER>\Desktop\map1`路径下，重复的文件跳过。需要注意的是文件名为14-16的文件夹无需复制。


