


# 在D:\map\路径下有很多zip压缩文件，压缩文件最里层为6-16数字命名的文件夹，将各个压缩文件最里层的6-15数字命名的文件夹复制到C:\Users\<USER>\Desktop\map\，重复的文件跳过。需要注意的是文件名为16的文件夹无需复制。

import zipfile
from pathlib import Path

def extract_folders_from_zip():
    """
    从D:\map\路径下的zip文件中提取数字命名为6-15的文件夹到目标目录
    合并同名文件夹，跳过同名文件
    """
    # 源目录和目标目录
    source_dir = Path(r"D:\map")
    target_dir = Path(r"C:\Users\<USER>\Desktop\map1")

    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)

    # 获取所有zip文件
    zip_files = list(source_dir.glob("*.zip"))
    print(f"找到 {len(zip_files)} 个zip文件")

    # 统计信息
    total_extracted_files = 0
    total_skipped_files = 0
    processed_folders = set()

    for zip_file in zip_files:
        print(f"\n正在处理: {zip_file.name}")

        try:
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                # 获取zip文件中的所有文件和文件夹路径
                all_paths = zip_ref.namelist()

                # 找到所有数字命名的文件夹（6-15）
                target_folders = {}  # folder_name -> full_path_in_zip

                for path in all_paths:
                    # 清理路径，移除末尾的斜杠
                    clean_path = path.rstrip('/')
                    path_parts = clean_path.split('/')

                    # 检查路径中的每个部分
                    for i, part in enumerate(path_parts):
                        if part.isdigit() and 6 <= int(part) <= 15:
                            folder_name = part
                            # 构建到该文件夹的完整路径
                            folder_path = '/'.join(path_parts[:i+1])

                            # 只保留最深层的路径（最里层的文件夹）
                            if folder_name not in target_folders or len(folder_path.split('/')) > len(target_folders[folder_name].split('/')):
                                target_folders[folder_name] = folder_path

                # 提取找到的文件夹
                for folder_name, folder_path_in_zip in target_folders.items():
                    target_folder = target_dir / folder_name

                    # 创建目标文件夹（如果不存在）
                    target_folder.mkdir(exist_ok=True)
                    processed_folders.add(folder_name)

                    print(f"  处理文件夹: {folder_name} (来源: {folder_path_in_zip})")

                    # 提取该文件夹下的所有文件
                    extracted_files = 0
                    skipped_files = 0

                    for file_path in all_paths:
                        # 检查文件是否在目标文件夹内
                        if file_path.startswith(folder_path_in_zip + '/'):
                            # 计算相对于文件夹的路径
                            relative_path = file_path[len(folder_path_in_zip) + 1:]

                            if relative_path:  # 不是空路径
                                target_file = target_folder / relative_path

                                # 如果是文件夹路径（以/结尾），创建目录
                                if file_path.endswith('/'):
                                    target_file.mkdir(parents=True, exist_ok=True)
                                else:
                                    # 检查文件是否已存在
                                    if target_file.exists():
                                        print(f"    跳过已存在的文件: {relative_path}")
                                        skipped_files += 1
                                        continue

                                    # 确保目标文件的目录存在
                                    target_file.parent.mkdir(parents=True, exist_ok=True)

                                    # 提取文件
                                    with zip_ref.open(file_path) as source_file:
                                        with open(target_file, 'wb') as target_file_obj:
                                            target_file_obj.write(source_file.read())
                                    extracted_files += 1

                    if extracted_files > 0 or skipped_files > 0:
                        print(f"    新提取: {extracted_files} 个文件, 跳过: {skipped_files} 个重复文件")

                    total_extracted_files += extracted_files
                    total_skipped_files += skipped_files

        except Exception as e:
            print(f"处理 {zip_file.name} 时出错: {e}")

    print(f"\n处理完成！")
    print(f"共处理了 {len(processed_folders)} 个文件夹:")
    for folder in sorted(processed_folders, key=int):
        print(f"  - {folder}")
    print(f"总计提取: {total_extracted_files} 个文件")
    print(f"总计跳过: {total_skipped_files} 个重复文件")

if __name__ == "__main__":
    extract_folders_from_zip()


